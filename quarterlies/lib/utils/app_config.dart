// This file is for development purposes and should not be committed to version control.
// For production, use a secure method to inject these values (e.g., Flutter build flavors, CI/CD secrets).

class AppConfig {
  static const String supabaseUrl = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: 'YOUR_SUPABASE_URL_HERE',
  );
  static const String supabaseAnonKey = String.fromEnvironment(
    'SUPABASE_ANON_KEY',
    defaultValue: 'YOUR_SUPABASE_ANON_KEY_HERE',
  );
  static const String supabaseServiceRoleKey = String.fromEnvironment(
    'SUPABASE_SERVICE_ROLE_KEY',
    defaultValue: 'YOUR_SUPABASE_SERVICE_ROLE_KEY_HERE',
  );
}
