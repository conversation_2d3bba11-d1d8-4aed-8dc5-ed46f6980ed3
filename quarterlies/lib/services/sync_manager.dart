import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/services/background_sync_service.dart';
import 'package:quarterlies/services/connectivity_service.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/local_database_service.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/utils/app_constants.dart';
import 'package:quarterlies/widgets/conflict_resolution_dialog.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:sqflite/sqflite.dart';
import 'package:quarterlies/services/sync_service.dart'
    as sync_service; // Import the simplified SyncService

/// Enum for categorizing sync errors
enum SyncErrorType { network, authentication, conflict, unknown }

/// SyncManager handles background synchronization and conflict resolution
/// for critical entities like Invoices, Expenses, and TaxPayments.
///
/// It provides methods for:
/// - Setting up background sync (platform-specific)
/// - Manual sync operations
/// - Conflict detection and resolution
/// - Offline search and filtering support
class SyncManager {
  // Singleton pattern
  static final SyncManager _instance = SyncManager._internal();
  factory SyncManager() => _instance;
  SyncManager._internal();

  final DataRepository _dataRepository = DataRepository();
  final LocalDatabaseService _localDatabaseService = LocalDatabaseService();
  final SupabaseService _supabaseService = SupabaseService();
  final ConnectivityService _connectivityService = ConnectivityService();
  final BackgroundSyncService _backgroundSyncService = BackgroundSyncService();
  final sync_service.SyncService _syncService =
      sync_service.SyncService(); // Inject SyncService

  bool _isInitialized = false;
  bool _isSyncing = false;

  // Reference to LoadingStateProvider for UI updates
  LoadingStateProvider? _loadingStateProvider;

  // Stream for sync status updates, exposed from SyncService
  Stream<SyncStatus> get syncStatusStream => _syncService.syncStatus;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  // Timer for periodic sync
  Timer? _periodicSyncTimer;

  // Set the LoadingStateProvider for UI updates
  void setLoadingStateProvider(LoadingStateProvider provider) {
    _loadingStateProvider = provider;
  }

  // Ensure resources are properly disposed
  void dispose() {
    _connectivitySubscription?.cancel();
    _periodicSyncTimer?.cancel(); // Dispose periodic sync timer
    _syncService.dispose(); // Dispose SyncService's controller
  }

  // Initialize the sync manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Removed _localDatabaseService.initialize() as DataRepository handles it.
    _connectivityService.initialize();

    // Set up listeners for connectivity changes
    _connectivitySubscription = _connectivityService.connectionStatus.listen((
      isConnected,
    ) {
      if (isConnected && !_isSyncing) {
        // Only attempt sync if not already syncing
        // When connection is restored, attempt to sync
        syncData();
      }
    });

    // Start periodic sync timer (every 5 minutes)
    _periodicSyncTimer = Timer.periodic(const Duration(minutes: 5), (_) async {
      final isConnected = await _connectivityService.checkConnection();
      if (isConnected && !_isSyncing) {
        // Only attempt sync if not already syncing
        syncData();
      }
    });

    // Set up platform-specific background sync
    await _setupBackgroundSync();

    _isInitialized = true;
  }

  // Set up platform-specific background sync
  Future<void> _setupBackgroundSync() async {
    // Initialize the background sync service
    // This handles platform-specific implementation (WorkManager for Android, Background Fetch for iOS)
    await _backgroundSyncService.initialize();

    debugPrint('Background sync service initialized');
  }

  // Manually trigger a sync operation
  Future<void> syncData() async {
    if (_isSyncing) return; // Prevent multiple concurrent syncs

    _isSyncing = true;
    _syncService.updateSyncStatus(
      SyncStatus.pending,
    ); // Use SyncService to update status

    // Use LoadingStateProvider if available
    if (_loadingStateProvider != null) {
      await _loadingStateProvider!.executeWithSyncLoading(() async {
        await _performSync();
      }, operationName: 'Syncing data...');
    } else {
      await _performSync();
    }
  }

  // Internal sync implementation
  Future<void> _performSync() async {
    try {
      if (!await _connectivityService.checkConnection()) {
        _syncService.updateSyncStatus(SyncStatus.error); // Use SyncService
        _isSyncing = false;
        return;
      }

      // Sync critical entities
      await _syncInvoices();
      await _syncExpenses();
      await _syncTaxPayments();
      await _syncEstimates();
      await _syncDocumentSigningRequestsWithConflictResolution();
      await _syncSignedDocumentsWithConflictResolution();

      // Sync other entities
      await _syncCustomers();
      await _syncJobs();
      await _syncTimeLogs();
      await _syncPayments();
      await _syncContracts();

      // Sync other entities via DataRepository
      // DataRepository's syncData will now call SyncManager.syncData, so this line is redundant
      // and would cause an infinite loop if not careful.
      // Instead, DataRepository should rely on SyncManager's ongoing syncs or trigger specific entity syncs if needed.
      // For now, I will remove this line to prevent the loop.
      // await _dataRepository.syncData();

      _syncService.updateSyncStatus(SyncStatus.synced); // Use SyncService
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'syncData'},
      );
      ErrorHandler.logError(appError);

      final errorType = _categorizeError(e);
      debugPrint('Error during sync (${errorType.name}): ${e.toString()}');
      _syncService.updateSyncStatus(SyncStatus.error); // Use SyncService

      // For network errors, schedule a retry
      if (errorType == SyncErrorType.network) {
        _scheduleRetry();
      }
      rethrow;
    } finally {
      _isSyncing = false;
    }
  }

  /// Categorize sync errors for better handling
  SyncErrorType _categorizeError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket')) {
      return SyncErrorType.network;
    } else if (errorString.contains('auth') ||
        errorString.contains('unauthorized') ||
        errorString.contains('forbidden')) {
      return SyncErrorType.authentication;
    } else if (errorString.contains('conflict') ||
        errorString.contains('version')) {
      return SyncErrorType.conflict;
    } else {
      return SyncErrorType.unknown;
    }
  }

  /// Schedule a retry for failed sync operations
  void _scheduleRetry() {
    Timer(Duration(minutes: AppConstants.syncRetryDelayMinutes), () async {
      if (await _connectivityService.checkConnection()) {
        debugPrint('Retrying sync after network error');
        syncData();
      }
    });
  }

  // Schedule a one-time sync operation
  Future<void> scheduleOneTimeSync() async {
    await _backgroundSyncService.scheduleOneTimeSync();
  }

  // Sync invoices with conflict detection
  Future<void> _syncInvoices() async {
    try {
      // Get all pending invoices
      final pendingInvoices =
          await _localDatabaseService.getPendingSyncInvoices();

      for (var invoiceMap in pendingInvoices) {
        try {
          // Convert Map to Invoice object
          // First, get line items for this invoice
          final db = await _localDatabaseService.database;
          final List<Map<String, dynamic>> itemMaps = await db.query(
            'invoice_items',
            where: 'invoice_id = ?',
            whereArgs: [invoiceMap['id']],
          );

          // Remove sync_status from item maps
          for (var itemMap in itemMaps) {
            itemMap.remove('sync_status');
          }

          // Add line items to invoice map
          invoiceMap['line_items'] = itemMaps;

          // Convert to Invoice object
          final invoice = Invoice.fromJson(invoiceMap);

          // Check if the invoice exists on the server
          final serverInvoiceMap = await _supabaseService.getInvoiceById(
            invoice.id!,
          );

          // Convert server response to Invoice object
          final serverInvoice = Invoice.fromJson(
            serverInvoiceMap as Map<String, dynamic>,
          );

          // Check if there's a conflict (both versions were modified)
          if (serverInvoice.updatedAt != null &&
              invoice.updatedAt != null &&
              serverInvoice.updatedAt!.isAfter(invoice.createdAt!)) {
            // Mark as conflict - needs user resolution
            final conflictInvoice = invoice.copyWith(
              syncStatus: SyncStatus.conflict,
            );

            // Use direct database update since updateInvoice doesn't exist
            final conflictMap = conflictInvoice.toJson();
            await db.update(
              'invoices',
              conflictMap,
              where: 'id = ?',
              whereArgs: [conflictInvoice.id],
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          } else {
            // No conflict, update server with local version
            await _supabaseService.updateInvoice(invoice);
            await _localDatabaseService.updateInvoiceSyncStatus(
              invoice.id!,
              SyncStatus.synced.name,
            );
          }
        } catch (e) {
          // Mark as error
          if (invoiceMap['id'] != null) {
            await _localDatabaseService.updateInvoiceSyncStatus(
              invoiceMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing invoice ${invoiceMap['id']}: ${e.toString()}',
            );
          } else {
            debugPrint('Error syncing invoice with null ID: ${e.toString()}');
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncInvoices: ${e.toString()}');
      rethrow;
    }
  }

  // Sync expenses with conflict detection
  Future<void> _syncExpenses() async {
    try {
      // Get all pending expenses
      final pendingExpenses =
          await _localDatabaseService.getPendingSyncExpenses();

      for (var expenseMap in pendingExpenses) {
        try {
          // Convert tags string back to list if needed
          if (expenseMap['tags'] != null && expenseMap['tags'] is String) {
            expenseMap['tags'] = expenseMap['tags'].split(',');
          }

          // Convert to Expense object
          final expense = Expense.fromJson(expenseMap);

          // Check if the expense exists on the server
          final serverExpenseMap = await _supabaseService.getExpenseById(
            expense.id,
          );

          // Convert server response to Expense object
          final serverExpense = Expense.fromJson(
            serverExpenseMap as Map<String, dynamic>,
          );

          // Check if there's a conflict (both versions were modified)
          if (serverExpense.updatedAt != null &&
              expense.updatedAt != null &&
              serverExpense.updatedAt!.isAfter(expense.createdAt)) {
            // Mark as conflict - needs user resolution
            final conflictExpense = expense.copyWith(
              syncStatus: SyncStatus.conflict,
            );
            await _localDatabaseService.updateExpense(conflictExpense);
          } else {
            // No conflict, update server with local version
            await _supabaseService.updateExpense(expense);
            await _localDatabaseService.updateExpenseSyncStatus(
              expense.id,
              SyncStatus.synced.name,
            );
          }
        } catch (e) {
          // Mark as error
          if (expenseMap['id'] != null) {
            await _localDatabaseService.updateExpenseSyncStatus(
              expenseMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing expense ${expenseMap['id']}: ${e.toString()}',
            );
          } else {
            debugPrint('Error syncing expense with null ID: ${e.toString()}');
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncExpenses: ${e.toString()}');
      rethrow;
    }
  }

  // Sync tax payments with conflict detection
  Future<void> _syncTaxPayments() async {
    try {
      // Get all pending tax payments
      final pendingTaxPayments =
          await _localDatabaseService.getPendingSyncTaxPayments();

      for (var taxPaymentMap in pendingTaxPayments) {
        try {
          // Convert to TaxPayment object
          final taxPayment = TaxPayment.fromJson(taxPaymentMap);

          // Check if the tax payment exists on the server
          final serverTaxPaymentMap = await _supabaseService.getTaxPaymentById(
            taxPayment.id,
          );

          // Convert server response to TaxPayment object
          final serverTaxPayment = TaxPayment.fromJson(
            serverTaxPaymentMap as Map<String, dynamic>,
          );

          // Check if there's a conflict (both versions were modified)
          if (serverTaxPayment.updatedAt.isAfter(taxPayment.createdAt)) {
            // Mark as conflict - needs user resolution
            final conflictTaxPayment = taxPayment.copyWith(
              syncStatus: SyncStatus.conflict,
            );
            await _localDatabaseService.updateTaxPayment(conflictTaxPayment);
          } else {
            // No conflict, update server with local version
            await _supabaseService.updateTaxPayment(taxPayment);
            await _localDatabaseService.updateTaxPaymentSyncStatus(
              taxPayment.id,
              SyncStatus.synced.name,
            );
          }
        } catch (e) {
          // Mark as error
          if (taxPaymentMap['id'] != null) {
            await _localDatabaseService.updateTaxPaymentSyncStatus(
              taxPaymentMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing tax payment ${taxPaymentMap['id']}: ${e.toString()}',
            );
          } else {
            debugPrint(
              'Error syncing tax payment with null ID: ${e.toString()}',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncTaxPayments: ${e.toString()}');
      rethrow;
    }
  }

  // Resolve an invoice conflict
  Future<void> resolveInvoiceConflict(
    String invoiceId, {
    required bool keepLocal,
  }) async {
    if (!await _connectivityService.checkConnection()) {
      throw Exception('Cannot resolve conflict while offline');
    }

    try {
      // Get invoice from local database
      final db = await _localDatabaseService.database;
      final List<Map<String, dynamic>> localInvoiceMaps = await db.query(
        'invoices',
        where: 'id = ?',
        whereArgs: [invoiceId],
        limit: 1,
      );

      if (localInvoiceMaps.isEmpty) {
        throw Exception('Cannot resolve conflict: missing local invoice');
      }

      // Get line items for this invoice
      final List<Map<String, dynamic>> itemMaps = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [invoiceId],
      );

      // Remove sync_status from item maps
      for (var itemMap in itemMaps) {
        itemMap.remove('sync_status');
      }

      // Add line items to invoice map
      final localInvoiceMap = localInvoiceMaps.first;
      localInvoiceMap['line_items'] = itemMaps;

      // Convert to Invoice object
      final localInvoice = Invoice.fromJson(localInvoiceMap);

      // Get server invoice
      final serverInvoiceMap = await _supabaseService.getInvoiceById(invoiceId);
      final serverInvoice = Invoice.fromJson(
        serverInvoiceMap as Map<String, dynamic>,
      );

      if (keepLocal) {
        // Keep local version - update server
        final resolvedInvoice = localInvoice.copyWith(
          syncStatus: SyncStatus.synced,
        );
        await _supabaseService.updateInvoice(resolvedInvoice);

        // Update local database directly
        final resolvedMap = resolvedInvoice.toJson();
        await db.update(
          'invoices',
          resolvedMap,
          where: 'id = ?',
          whereArgs: [resolvedInvoice.id],
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      } else {
        // Keep server version - update local
        final resolvedInvoice = serverInvoice.copyWith(
          syncStatus: SyncStatus.synced,
        );

        // Update local database directly
        final resolvedMap = resolvedInvoice.toJson();
        await db.update(
          'invoices',
          resolvedMap,
          where: 'id = ?',
          whereArgs: [resolvedInvoice.id],
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'resolveInvoiceConflict',
          'invoiceId': invoiceId,
          'keepLocal': keepLocal,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Resolve an expense conflict
  Future<void> resolveExpenseConflict(
    String expenseId, {
    required bool keepLocal,
  }) async {
    if (!await _connectivityService.checkConnection()) {
      throw Exception('Cannot resolve conflict while offline');
    }

    try {
      // Get expense from local database
      final db = await _localDatabaseService.database;
      final List<Map<String, dynamic>> localExpenseMaps = await db.query(
        'expenses',
        where: 'id = ?',
        whereArgs: [expenseId],
        limit: 1,
      );

      if (localExpenseMaps.isEmpty) {
        throw Exception('Cannot resolve conflict: missing local expense');
      }

      final localExpenseMap = localExpenseMaps.first;

      // Convert tags string back to list if needed
      if (localExpenseMap['tags'] != null &&
          localExpenseMap['tags'] is String) {
        localExpenseMap['tags'] = localExpenseMap['tags'].split(',');
      }

      // Convert to Expense object
      final localExpense = Expense.fromJson(localExpenseMap);

      // Get server expense
      final serverExpenseMap = await _supabaseService.getExpenseById(expenseId);
      final serverExpense = Expense.fromJson(
        serverExpenseMap as Map<String, dynamic>,
      );

      if (keepLocal) {
        // Keep local version - update server
        final resolvedExpense = localExpense.copyWith(
          syncStatus: SyncStatus.synced,
        );
        await _supabaseService.updateExpense(resolvedExpense);

        // Update local database directly
        final resolvedMap = resolvedExpense.toJson();

        // Convert tags list to string for storage
        if (resolvedMap['tags'] != null) {
          resolvedMap['tags'] = resolvedMap['tags'].join(',');
        }

        await db.update(
          'expenses',
          resolvedMap,
          where: 'id = ?',
          whereArgs: [resolvedExpense.id],
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      } else {
        // Keep server version - update local
        final resolvedExpense = serverExpense.copyWith(
          syncStatus: SyncStatus.synced,
        );

        // Update local database directly
        final resolvedMap = resolvedExpense.toJson();

        // Convert tags list to string for storage
        if (resolvedMap['tags'] != null) {
          resolvedMap['tags'] = resolvedMap['tags'].join(',');
        }

        await db.update(
          'expenses',
          resolvedMap,
          where: 'id = ?',
          whereArgs: [resolvedExpense.id],
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    } catch (e) {
      debugPrint('Error resolving expense conflict: ${e.toString()}');
      rethrow;
    }
  }

  // Resolve a tax payment conflict
  Future<void> resolveTaxPaymentConflict(
    String taxPaymentId, {
    required bool keepLocal,
  }) async {
    if (!await _connectivityService.checkConnection()) {
      throw Exception('Cannot resolve conflict while offline');
    }

    try {
      // Get tax payment from local database
      final db = await _localDatabaseService.database;
      final List<Map<String, dynamic>> localTaxPaymentMaps = await db.query(
        'tax_payments',
        where: 'id = ?',
        whereArgs: [taxPaymentId],
        limit: 1,
      );

      if (localTaxPaymentMaps.isEmpty) {
        throw Exception('Cannot resolve conflict: missing local tax payment');
      }

      final localTaxPaymentMap = localTaxPaymentMaps.first;

      // Convert to TaxPayment object
      final localTaxPayment = TaxPayment.fromJson(localTaxPaymentMap);

      // Get server tax payment
      final serverTaxPaymentMap = await _supabaseService.getTaxPaymentById(
        taxPaymentId,
      );
      final serverTaxPayment = TaxPayment.fromJson(
        serverTaxPaymentMap as Map<String, dynamic>,
      );

      if (keepLocal) {
        // Keep local version - update server
        final resolvedTaxPayment = localTaxPayment.copyWith(
          syncStatus: SyncStatus.synced,
        );
        await _supabaseService.updateTaxPayment(resolvedTaxPayment);

        // Update local database directly
        final resolvedMap = resolvedTaxPayment.toJson();
        await db.update(
          'tax_payments',
          resolvedMap,
          where: 'id = ?',
          whereArgs: [resolvedTaxPayment.id],
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      } else {
        // Keep server version - update local
        final resolvedTaxPayment = serverTaxPayment.copyWith(
          syncStatus: SyncStatus.synced,
        );

        // Update local database directly
        final resolvedMap = resolvedTaxPayment.toJson();
        await db.update(
          'tax_payments',
          resolvedMap,
          where: 'id = ?',
          whereArgs: [resolvedTaxPayment.id],
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    } catch (e) {
      debugPrint('Error resolving tax payment conflict: ${e.toString()}');
      rethrow;
    }
  }

  // Show conflict resolution dialog for an invoice
  Future<void> showInvoiceConflictDialog(
    BuildContext context,
    String invoiceId,
  ) async {
    try {
      // Get invoice from local database
      final db = await _localDatabaseService.database;
      final List<Map<String, dynamic>> localInvoiceMaps = await db.query(
        'invoices',
        where: 'id = ?',
        whereArgs: [invoiceId],
        limit: 1,
      );

      if (localInvoiceMaps.isEmpty) {
        throw Exception('Cannot show conflict dialog: missing local invoice');
      }

      // Get line items for this invoice
      final List<Map<String, dynamic>> itemMaps = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [invoiceId],
      );

      // Remove sync_status from item maps
      for (var itemMap in itemMaps) {
        itemMap.remove('sync_status');
      }

      // Add line items to invoice map
      final localInvoiceMap = localInvoiceMaps.first;
      localInvoiceMap['line_items'] = itemMaps;

      // Convert to Invoice object
      final localInvoice = Invoice.fromJson(localInvoiceMap);

      // Get server invoice
      final serverInvoiceMap = await _supabaseService.getInvoiceById(invoiceId);
      final serverInvoice = Invoice.fromJson(
        serverInvoiceMap as Map<String, dynamic>,
      );

      if (context.mounted) {
        showDialog(
          context: context,
          builder:
              (context) => ConflictResolutionDialog<Invoice>(
                localVersion: localInvoice,
                serverVersion: serverInvoice,
                title: 'Invoice Conflict',
                entityType: 'invoice',
                onResolved: (keepLocal) async {
                  await resolveInvoiceConflict(invoiceId, keepLocal: keepLocal);
                },
                entityDetailsBuilder:
                    (invoice) => _buildInvoiceDetails(invoice),
              ),
        );
      }
    } catch (e) {
      debugPrint('Error showing invoice conflict dialog: ${e.toString()}');
      rethrow;
    }
  }

  // Show conflict resolution dialog for an expense
  Future<void> showExpenseConflictDialog(
    BuildContext context,
    String expenseId,
  ) async {
    try {
      // Get expense from local database
      final db = await _localDatabaseService.database;
      final List<Map<String, dynamic>> localExpenseMaps = await db.query(
        'expenses',
        where: 'id = ?',
        whereArgs: [expenseId],
        limit: 1,
      );

      if (localExpenseMaps.isEmpty) {
        throw Exception('Cannot show conflict dialog: missing local expense');
      }

      final localExpenseMap = localExpenseMaps.first;

      // Convert tags string back to list if needed
      if (localExpenseMap['tags'] != null &&
          localExpenseMap['tags'] is String) {
        localExpenseMap['tags'] = localExpenseMap['tags'].split(',');
      }

      // Convert to Expense object
      final localExpense = Expense.fromJson(localExpenseMap);

      // Get server expense
      final serverExpenseMap = await _supabaseService.getExpenseById(expenseId);
      final serverExpense = Expense.fromJson(
        serverExpenseMap as Map<String, dynamic>,
      );

      if (context.mounted) {
        showDialog(
          context: context,
          builder:
              (context) => ConflictResolutionDialog<Expense>(
                localVersion: localExpense,
                serverVersion: serverExpense,
                title: 'Expense Conflict',
                entityType: 'expense',
                onResolved: (keepLocal) async {
                  await resolveExpenseConflict(expenseId, keepLocal: keepLocal);
                },
                entityDetailsBuilder:
                    (expense) => _buildExpenseDetails(expense),
              ),
        );
      }
    } catch (e) {
      debugPrint('Error showing expense conflict dialog: ${e.toString()}');
      rethrow;
    }
  }

  // Show conflict resolution dialog for a tax payment
  Future<void> showTaxPaymentConflictDialog(
    BuildContext context,
    String taxPaymentId,
  ) async {
    try {
      // Get tax payment from local database
      final db = await _localDatabaseService.database;
      final List<Map<String, dynamic>> localTaxPaymentMaps = await db.query(
        'tax_payments',
        where: 'id = ?',
        whereArgs: [taxPaymentId],
        limit: 1,
      );

      if (localTaxPaymentMaps.isEmpty) {
        throw Exception(
          'Cannot show conflict dialog: missing local tax payment',
        );
      }

      final localTaxPaymentMap = localTaxPaymentMaps.first;

      // Convert to TaxPayment object
      final localTaxPayment = TaxPayment.fromJson(localTaxPaymentMap);

      // Get server tax payment
      final serverTaxPaymentMap = await _supabaseService.getTaxPaymentById(
        taxPaymentId,
      );
      final serverTaxPayment = TaxPayment.fromJson(
        serverTaxPaymentMap as Map<String, dynamic>,
      );

      if (context.mounted) {
        showDialog(
          context: context,
          builder:
              (context) => ConflictResolutionDialog<TaxPayment>(
                localVersion: localTaxPayment,
                serverVersion: serverTaxPayment,
                title: 'Tax Payment Conflict',
                entityType: 'tax payment',
                onResolved: (keepLocal) async {
                  await resolveTaxPaymentConflict(
                    taxPaymentId,
                    keepLocal: keepLocal,
                  );
                },
                entityDetailsBuilder:
                    (taxPayment) => _buildTaxPaymentDetails(taxPayment),
              ),
        );
      }
    } catch (e) {
      debugPrint('Error showing tax payment conflict dialog: ${e.toString()}');
      rethrow;
    }
  }

  // Build invoice details widget
  Widget _buildInvoiceDetails(Invoice invoice) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Amount: \$${invoice.totalAmount.toStringAsFixed(2)}'),
        Text('Status: ${invoice.status}'),
        Text('Issue Date: ${_formatDate(invoice.issueDate)}'),
        Text('Due Date: ${_formatDate(invoice.dueDate)}'),
        if (invoice.notes != null && invoice.notes!.isNotEmpty)
          Text('Notes: ${invoice.notes}'),
        Text(
          'Last Updated: ${_formatDate(invoice.updatedAt ?? DateTime.now())}',
        ),
      ],
    );
  }

  // Build expense details widget
  Widget _buildExpenseDetails(Expense expense) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Amount: \$${expense.amount.toStringAsFixed(2)}'),
        Text('Category: ${expense.category ?? "Uncategorized"}'),
        Text('Date: ${_formatDate(expense.date)}'),
        if (expense.description.isNotEmpty)
          Text('Description: ${expense.description}'),
        Text('Last Updated: ${_formatDate(expense.updatedAt)}'),
      ],
    );
  }

  // Build tax payment details widget
  Widget _buildTaxPaymentDetails(TaxPayment taxPayment) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Amount: \$${taxPayment.amount.toStringAsFixed(2)}'),
        Text('Tax Period: ${taxPayment.taxPeriod}'),
        Text('Date: ${_formatDate(taxPayment.date)}'),
        if (taxPayment.paymentMethod != null)
          Text('Payment Method: ${taxPayment.paymentMethod}'),
        if (taxPayment.notes != null && taxPayment.notes!.isNotEmpty)
          Text('Notes: ${taxPayment.notes}'),
        Text('Last Updated: ${_formatDate(taxPayment.updatedAt)}'),
      ],
    );
  }

  // Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.month}/${date.day}/${date.year}';
  }

  // Sync document signing requests with conflict detection
  Future<void> _syncDocumentSigningRequestsWithConflictResolution() async {
    try {
      // Get all pending document signing requests
      final pendingRequests =
          await _localDatabaseService.getPendingSyncDocumentSigningRequests();

      for (var requestMap in pendingRequests) {
        try {
          // Convert to DocumentSigningRequest object
          final request = DocumentSigningRequest.fromJson(requestMap);

          // For now, just mark as synced since we don't have server-side implementation yet
          // In a full implementation, you would:
          // 1. Upload the PDF to Supabase storage
          // 2. Create the signing request record on the server
          // 3. Send the email via edge function
          // 4. Handle conflicts if the request was modified on both sides

          await _localDatabaseService.updateDocumentSigningRequestSyncStatus(
            request.id,
            SyncStatus.synced.name,
          );

          debugPrint('Synced document signing request: ${request.id}');
        } catch (e) {
          // Mark as error
          if (requestMap['id'] != null) {
            await _localDatabaseService.updateDocumentSigningRequestSyncStatus(
              requestMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing document signing request ${requestMap['id']}: ${e.toString()}',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncDocumentSigningRequests: ${e.toString()}');
      rethrow;
    }
  }

  // Sync signed documents with conflict detection
  Future<void> _syncSignedDocumentsWithConflictResolution() async {
    try {
      // Get all pending signed documents
      final pendingDocuments =
          await _localDatabaseService.getPendingSyncSignedDocuments();

      for (var documentMap in pendingDocuments) {
        try {
          // Convert to SignedDocument object
          final document = SignedDocument.fromJson(documentMap);

          // For now, just mark as synced since we don't have server-side implementation yet
          // In a full implementation, you would:
          // 1. Upload the customer and contractor PDFs to Supabase storage
          // 2. Create the signed document record on the server
          // 3. Update the signing request status
          // 4. Handle conflicts if the document was modified on both sides

          await _localDatabaseService.updateSignedDocumentSyncStatus(
            document.id,
            SyncStatus.synced.name,
          );

          debugPrint('Synced signed document: ${document.id}');
        } catch (e) {
          // Mark as error
          if (documentMap['id'] != null) {
            await _localDatabaseService.updateSignedDocumentSyncStatus(
              documentMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing signed document ${documentMap['id']}: ${e.toString()}',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncSignedDocuments: ${e.toString()}');
      rethrow;
    }
  }

  // Sync estimates with conflict detection
  Future<void> _syncEstimates() async {
    try {
      // Get all pending estimates
      final pendingEstimates =
          await _localDatabaseService.getPendingSyncEstimates();

      for (var estimateMap in pendingEstimates) {
        try {
          // Convert to Estimate object
          final estimate = Estimate.fromJson(estimateMap);

          // Check if the estimate exists on the server
          try {
            final serverEstimate = await _supabaseService.getEstimateById(
              estimate.id,
            );

            // Check if there's a conflict (both versions were modified)
            if (serverEstimate.updatedAt != null &&
                estimate.updatedAt != null &&
                serverEstimate.updatedAt!.isAfter(estimate.createdAt)) {
              // Mark as conflict - needs user resolution
              await _localDatabaseService.updateEstimateSyncStatus(
                estimate.id,
                SyncStatus.conflict.name,
              );
            } else {
              // No conflict, update server with local version
              await _supabaseService.updateEstimate(estimate);
              await _localDatabaseService.updateEstimateSyncStatus(
                estimate.id,
                SyncStatus.synced.name,
              );
            }
          } catch (e) {
            // Estimate doesn't exist on server, create it
            await _supabaseService.addEstimate(estimate);
            await _localDatabaseService.updateEstimateSyncStatus(
              estimate.id,
              SyncStatus.synced.name,
            );
          }

          debugPrint('Synced estimate: ${estimate.id}');
        } catch (e) {
          // Mark as error
          if (estimateMap['id'] != null) {
            await _localDatabaseService.updateEstimateSyncStatus(
              estimateMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing estimate ${estimateMap['id']}: ${e.toString()}',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncEstimates: ${e.toString()}');
      rethrow;
    }
  }

  // Sync customers with conflict detection
  Future<void> _syncCustomers() async {
    try {
      // Get all pending customers
      final pendingCustomers =
          await _localDatabaseService.getPendingSyncCustomers();

      for (var customerMap in pendingCustomers) {
        try {
          // Convert to Customer object
          customerMap.remove('sync_status');
          final customer = Customer.fromJson(customerMap);

          // Check if the customer exists on the server
          try {
            final serverCustomer = await _supabaseService.getCustomerById(
              customer.id,
            );

            // Check if there's a conflict (both versions were modified)
            if (serverCustomer.updatedAt != null &&
                customer.updatedAt != null &&
                serverCustomer.updatedAt!.isAfter(customer.createdAt)) {
              // Mark as conflict - needs user resolution
              await _localDatabaseService.updateCustomerSyncStatus(
                customer.id,
                SyncStatus.conflict.name,
              );
            } else {
              // No conflict, update server with local version
              await _supabaseService.updateCustomer(customer);
              await _localDatabaseService.updateCustomerSyncStatus(
                customer.id,
                SyncStatus.synced.name,
              );
            }
          } catch (e) {
            // Customer doesn't exist on server, create it
            await _supabaseService.addCustomer(customer);
            await _localDatabaseService.updateCustomerSyncStatus(
              customer.id,
              SyncStatus.synced.name,
            );
          }

          debugPrint('Synced customer: ${customer.id}');
        } catch (e) {
          // Mark as error
          if (customerMap['id'] != null) {
            await _localDatabaseService.updateCustomerSyncStatus(
              customerMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing customer ${customerMap['id']}: ${e.toString()}',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncCustomers: ${e.toString()}');
      rethrow;
    }
  }

  // Sync jobs with conflict detection
  Future<void> _syncJobs() async {
    try {
      // Get all pending jobs
      final pendingJobs = await _localDatabaseService.getPendingSyncJobs();

      for (var jobMap in pendingJobs) {
        try {
          // Convert to Job object
          jobMap.remove('sync_status');
          final job = Job.fromJson(jobMap);

          // Check if the job exists on the server
          try {
            final serverJob = await _supabaseService.getJobById(job.id);

            // Check if there's a conflict (both versions were modified)
            if (serverJob.updatedAt != null &&
                job.updatedAt != null &&
                serverJob.updatedAt!.isAfter(job.createdAt)) {
              // Mark as conflict - needs user resolution
              await _localDatabaseService.updateJobSyncStatus(
                job.id,
                SyncStatus.conflict.name,
              );
            } else {
              // No conflict, update server with local version
              await _supabaseService.updateJob(job);
              await _localDatabaseService.updateJobSyncStatus(
                job.id,
                SyncStatus.synced.name,
              );
            }
          } catch (e) {
            // Job doesn't exist on server, create it
            await _supabaseService.addJob(job);
            await _localDatabaseService.updateJobSyncStatus(
              job.id,
              SyncStatus.synced.name,
            );
          }

          debugPrint('Synced job: ${job.id}');
        } catch (e) {
          // Mark as error
          if (jobMap['id'] != null) {
            await _localDatabaseService.updateJobSyncStatus(
              jobMap['id'],
              SyncStatus.error.name,
            );
            debugPrint('Error syncing job ${jobMap['id']}: ${e.toString()}');
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncJobs: ${e.toString()}');
      rethrow;
    }
  }

  // Sync time logs with conflict detection
  Future<void> _syncTimeLogs() async {
    try {
      // Get all pending time logs
      final pendingTimeLogs =
          await _localDatabaseService.getPendingSyncTimeLogs();

      for (var timeLogMap in pendingTimeLogs) {
        try {
          // Convert to TimeLog object
          timeLogMap.remove('sync_status');
          final timeLog = TimeLog.fromJson(timeLogMap);

          // Check if the time log exists on the server
          try {
            final serverTimeLog = await _supabaseService.getTimeLogById(
              timeLog.id,
            );

            // Check if there's a conflict (both versions were modified)
            if (serverTimeLog != null &&
                serverTimeLog.updatedAt != null &&
                timeLog.updatedAt != null &&
                serverTimeLog.updatedAt!.isAfter(timeLog.createdAt)) {
              // Mark as conflict - needs user resolution
              await _localDatabaseService.updateTimeLogSyncStatus(
                timeLog.id,
                SyncStatus.conflict.name,
              );
            } else {
              // No conflict, update server with local version
              await _supabaseService.updateTimeLog(timeLog);
              await _localDatabaseService.updateTimeLogSyncStatus(
                timeLog.id,
                SyncStatus.synced.name,
              );
            }
          } catch (e) {
            // Time log doesn't exist on server, create it
            await _supabaseService.addTimeLog(timeLog);
            await _localDatabaseService.updateTimeLogSyncStatus(
              timeLog.id,
              SyncStatus.synced.name,
            );
          }

          debugPrint('Synced time log: ${timeLog.id}');
        } catch (e) {
          // Mark as error
          if (timeLogMap['id'] != null) {
            await _localDatabaseService.updateTimeLogSyncStatus(
              timeLogMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing time log ${timeLogMap['id']}: ${e.toString()}',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncTimeLogs: ${e.toString()}');
      rethrow;
    }
  }

  // Sync payments with conflict detection
  Future<void> _syncPayments() async {
    try {
      // Get all pending payments
      final pendingPayments =
          await _localDatabaseService.getPendingSyncPayments();

      for (var paymentMap in pendingPayments) {
        try {
          // Convert to Payment object
          paymentMap.remove('sync_status');
          final payment = Payment.fromJson(paymentMap);

          // Check if the payment exists on the server
          try {
            // For now, just sync the payment to server
            // In a full implementation, you would check for conflicts
            await _supabaseService.updatePayment(payment);
            await _localDatabaseService.updatePaymentSyncStatus(
              payment.id,
              SyncStatus.synced.name,
            );
          } catch (e) {
            // Payment doesn't exist on server, create it
            await _supabaseService.addPayment(payment);
            await _localDatabaseService.updatePaymentSyncStatus(
              payment.id,
              SyncStatus.synced.name,
            );
          }

          debugPrint('Synced payment: ${payment.id}');
        } catch (e) {
          // Mark as error
          if (paymentMap['id'] != null) {
            await _localDatabaseService.updatePaymentSyncStatus(
              paymentMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing payment ${paymentMap['id']}: ${e.toString()}',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncPayments: ${e.toString()}');
      rethrow;
    }
  }

  // Sync contracts with conflict detection
  Future<void> _syncContracts() async {
    try {
      // Get all pending contracts
      final pendingContracts =
          await _localDatabaseService.getPendingSyncContracts();

      for (var contractMap in pendingContracts) {
        try {
          // Convert to Contract object
          contractMap.remove('sync_status');
          final contract = Contract.fromJson(contractMap);

          // Check if the contract exists on the server
          try {
            final serverContract = await _supabaseService.getContractById(
              contract.id,
            );

            // Check if there's a conflict (both versions were modified)
            if (serverContract.updatedAt != null &&
                contract.updatedAt != null &&
                serverContract.updatedAt!.isAfter(contract.createdAt)) {
              // Mark as conflict - needs user resolution
              await _localDatabaseService.updateContractSyncStatus(
                contract.id,
                SyncStatus.conflict.name,
              );
            } else {
              // No conflict, update server with local version
              await _supabaseService.updateContract(contract);
              await _localDatabaseService.updateContractSyncStatus(
                contract.id,
                SyncStatus.synced.name,
              );
            }
          } catch (e) {
            // Contract doesn't exist on server, create it
            await _supabaseService.addContract(contract);
            await _localDatabaseService.updateContractSyncStatus(
              contract.id,
              SyncStatus.synced.name,
            );
          }

          debugPrint('Synced contract: ${contract.id}');
        } catch (e) {
          // Mark as error
          if (contractMap['id'] != null) {
            await _localDatabaseService.updateContractSyncStatus(
              contractMap['id'],
              SyncStatus.error.name,
            );
            debugPrint(
              'Error syncing contract ${contractMap['id']}: ${e.toString()}',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error in _syncContracts: ${e.toString()}');
      rethrow;
    }
  }
}
