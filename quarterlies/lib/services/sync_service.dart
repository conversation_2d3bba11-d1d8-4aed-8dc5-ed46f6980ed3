import 'dart:async';
import 'package:quarterlies/models/sync_status.dart'; // Import the enum directly

class SyncService {
  // Singleton pattern
  static final SyncService _instance = SyncService._internal();
  factory SyncService() {
    return _instance;
  }
  SyncService._internal();

  // Stream controller for sync status updates
  final _syncStatusController = StreamController<SyncStatus>.broadcast();
  Stream<SyncStatus> get syncStatus => _syncStatusController.stream;

  // Method to update sync status
  void updateSyncStatus(SyncStatus status) {
    _syncStatusController.add(status);
  }

  // Dispose resources
  void dispose() {
    _syncStatusController.close();
  }
}
